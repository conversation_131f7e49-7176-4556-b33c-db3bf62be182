{"contractName": "SecondaryMarket", "abi": [{"inputs": [{"internalType": "address", "name": "_usdtToken", "type": "address"}, {"internalType": "address", "name": "_blocksToken", "type": "address"}, {"internalType": "address", "name": "_router", "type": "address"}, {"internalType": "address", "name": "_factory", "type": "address"}, {"internalType": "address", "name": "_feeRecipient", "type": "address"}, {"internalType": "address", "name": "admin", "type": "address"}, {"internalType": "uint256", "name": "_targetPrice", "type": "uint256"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "blocksAmount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "usdtAmount", "type": "uint256"}], "name": "TokensSwapped", "type": "event"}, {"inputs": [], "name": "factory", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "feeRecipient", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "blocksAmount", "type": "uint256"}], "name": "getSwapQuote", "outputs": [{"internalType": "uint256", "name": "usdtAmount", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "router", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "blocksAmount", "type": "uint256"}, {"internalType": "uint256", "name": "minUsdtAmount", "type": "uint256"}], "name": "swapBLOCKSForUSDT", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "usdtAmount", "type": "uint256"}, {"internalType": "uint256", "name": "minBlocksAmount", "type": "uint256"}], "name": "swapUSDTForBLOCKS", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "targetPrice", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "token", "outputs": [{"internalType": "contract IERC20", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "usdtToken", "outputs": [{"internalType": "contract IERC20", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}]}