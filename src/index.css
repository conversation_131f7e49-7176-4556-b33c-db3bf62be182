@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom animations for BLOCKS token logo */
@keyframes float {
  0%, 100% {
    transform: translateY(0px) scale(1);
  }
  25% {
    transform: translateY(-8px) scale(1.02);
  }
  50% {
    transform: translateY(-12px) scale(1.05);
  }
  75% {
    transform: translateY(-8px) scale(1.02);
  }
}

.animate-float {
  animation: float 4s ease-in-out infinite;
}

/* Respect user's motion preferences */
@media (prefers-reduced-motion: reduce) {
  .animate-float {
    animation: none;
  }
}

/* Custom scrollbar styles for better UX */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
