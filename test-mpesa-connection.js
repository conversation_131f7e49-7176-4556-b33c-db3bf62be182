// Test script to verify M-Pesa API connection from frontend perspective
import axios from 'axios';

const API_BASE_URL = 'http://localhost:3001/api';

// Test data - simulating frontend request
const testPaymentData = {
  walletAddress: '******************************************',
  packageId: 1,
  phoneNumber: '254712345678',
  amount: 100,
  referrerAddress: '******************************************'
};

// Test data that might cause issues
const problematicTestData = [
  {
    name: 'Missing walletAddress',
    data: {
      packageId: 1,
      phoneNumber: '254712345678',
      amount: 100
    }
  },
  {
    name: 'Invalid walletAddress format',
    data: {
      walletAddress: 'invalid-address',
      packageId: 1,
      phoneNumber: '254712345678',
      amount: 100
    }
  },
  {
    name: 'Invalid phone number format',
    data: {
      walletAddress: '******************************************',
      packageId: 1,
      phoneNumber: '0712345678', // Missing 254 prefix
      amount: 100
    }
  },
  {
    name: 'String packageId instead of number',
    data: {
      walletAddress: '******************************************',
      packageId: '1', // String instead of number
      phoneNumber: '254712345678',
      amount: 100
    }
  },
  {
    name: 'String amount instead of number',
    data: {
      walletAddress: '******************************************',
      packageId: 1,
      phoneNumber: '254712345678',
      amount: '100' // String instead of number
    }
  },
  {
    name: 'Zero amount',
    data: {
      walletAddress: '******************************************',
      packageId: 1,
      phoneNumber: '254712345678',
      amount: 0
    }
  },
  {
    name: 'Negative amount',
    data: {
      walletAddress: '******************************************',
      packageId: 1,
      phoneNumber: '254712345678',
      amount: -100
    }
  }
];

async function testProblematicCases() {
  console.log('🔍 Testing problematic cases that might cause 400 errors...\n');

  for (const testCase of problematicTestData) {
    try {
      console.log(`Testing: ${testCase.name}`);
      const response = await axios.post(`${API_BASE_URL}/mpesa/initiate-payment`, testCase.data);
      console.log(`❌ Expected error but got success:`, response.data);
    } catch (error) {
      if (error.response?.status === 400) {
        console.log(`✅ Got expected 400 error:`, error.response.data);
      } else {
        console.log(`❌ Got unexpected error:`, error.response?.data || error.message);
      }
    }
    console.log('');
  }
}

async function testMpesaConnection() {
  console.log('🧪 Testing M-Pesa API Connection...\n');

  try {
    // Test 1: Health check
    console.log('1️⃣ Testing health endpoint...');
    const healthResponse = await axios.get(`${API_BASE_URL.replace('/api', '')}/health`);
    console.log('✅ Health check passed:', healthResponse.data);
    console.log('');

    // Test 2: Initiate payment
    console.log('2️⃣ Testing payment initiation...');
    const paymentResponse = await axios.post(`${API_BASE_URL}/mpesa/initiate-payment`, testPaymentData);
    console.log('✅ Payment initiation passed:', paymentResponse.data);
    console.log('');

    // Test 3: Query payment status
    console.log('3️⃣ Testing payment status query...');
    const statusResponse = await axios.get(`${API_BASE_URL}/mpesa/status/${paymentResponse.data.checkoutRequestId}`);
    console.log('✅ Payment status query passed:', statusResponse.data);
    console.log('');

    // Test 4: Get transaction history
    console.log('4️⃣ Testing transaction history...');
    const historyResponse = await axios.get(`${API_BASE_URL}/mpesa/transactions/${testPaymentData.walletAddress}?limit=10&offset=0`);
    console.log('✅ Transaction history passed:', historyResponse.data);
    console.log('');

    // Test 5: Test CORS headers
    console.log('5️⃣ Testing CORS headers...');
    const corsResponse = await axios.options(`${API_BASE_URL}/mpesa/initiate-payment`);
    console.log('✅ CORS test passed. Status:', corsResponse.status);
    console.log('');

    console.log('🎉 All M-Pesa API tests passed successfully!');
    console.log('✅ Frontend can successfully connect to backend M-Pesa API');
    console.log('✅ The ERR_CONNECTION_REFUSED error should now be resolved');

    // Test problematic cases
    await testProblematicCases();

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
    if (error.code === 'ECONNREFUSED') {
      console.error('🔴 Connection refused - make sure the backend server is running on port 3001');
    }
  }
}

// Run the test
testMpesaConnection();
