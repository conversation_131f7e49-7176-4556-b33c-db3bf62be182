{"numTotalTestSuites": 4, "numPassedTestSuites": 4, "numFailedTestSuites": 0, "numPendingTestSuites": 0, "numTotalTests": 5, "numPassedTests": 5, "numFailedTests": 0, "numPendingTests": 0, "numTodoTests": 0, "snapshot": {"added": 0, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0, "didUpdate": false}, "startTime": 1752924793003, "success": true, "testResults": [{"assertionResults": [{"ancestorTitles": ["Balance Correction Debug", "SwapCard Balance Issues"], "fullName": "Balance Correction Debug SwapCard Balance Issues should correct inflated BLOCKS balance (5+ trillion to ~50)", "status": "passed", "title": "should correct inflated BLOCKS balance (5+ trillion to ~50)", "duration": 31.137190999999802, "failureMessages": [], "location": {"line": 47, "column": 5}, "meta": {}}, {"ancestorTitles": ["Balance Correction Debug", "SwapCard Balance Issues"], "fullName": "Balance Correction Debug SwapCard Balance Issues should not correct USDT balance (should show actual value)", "status": "passed", "title": "should not correct USDT balance (should show actual value)", "duration": 1.7729179999996632, "failureMessages": [], "location": {"line": 67, "column": 5}, "meta": {}}, {"ancestorTitles": ["Balance Correction Debug", "SwapCard Balance Issues"], "fullName": "Balance Correction Debug SwapCard Balance Issues should detect when correction is needed", "status": "passed", "title": "should detect when correction is needed", "duration": 2.700463000000127, "failureMessages": [], "location": {"line": 83, "column": 5}, "meta": {}}, {"ancestorTitles": ["Balance Correction Debug", "SwapCard Balance Issues"], "fullName": "Balance Correction Debug SwapCard Balance Issues should calculate wallet correction flag correctly", "status": "passed", "title": "should calculate wallet correction flag correctly", "duration": 1.782104999999774, "failureMessages": [], "location": {"line": 102, "column": 5}, "meta": {}}, {"ancestorTitles": ["Balance Correction Debug", "Real-world Scenarios"], "fullName": "Balance Correction Debug Real-world Scenarios should handle the exact SwapCard scenario", "status": "passed", "title": "should handle the exact SwapCard scenario", "duration": 0.8937120000000505, "failureMessages": [], "location": {"line": 124, "column": 5}, "meta": {}}], "startTime": 1752924797904, "endTime": 1752924797943.8938, "status": "passed", "message": "", "name": "/home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/src/tests/balance-correction-debug.test.ts"}]}