const hre = require("hardhat");

async function main() {
  console.log("🔍 Debugging Vesting Issue...");
  
  // Get the test user address
  const testUser = "******************************************";
  console.log("Test user:", testUser);
  
  // Load contract addresses from deployments
  const deployments = require('./deployments/deployments-corrected-portfolio-metrics.json');

  // Get contracts
  const vestingVault = await hre.ethers.getContractAt("VestingVault", deployments.contracts.VestingVault);
  const packageManager = await hre.ethers.getContractAt("PackageManagerV2_1", deployments.contracts.PackageManagerV2_1);
  
  console.log("\n📊 Current Vesting Data:");
  
  // Get raw vesting data
  const totalLocked = await vestingVault.totalLocked(testUser);
  const released = await vestingVault.released(testUser);
  const vestedAmount = await vestingVault.vestedAmount(testUser);
  const schedule = await vestingVault.userSchedule(testUser);
  
  console.log("Raw blockchain values:");
  console.log("- Total Locked:", totalLocked.toString(), "wei");
  console.log("- Total Locked:", hre.ethers.formatUnits(totalLocked, 18), "BLOCKS");
  console.log("- Released:", hre.ethers.formatUnits(released, 18), "BLOCKS");
  console.log("- Vested Amount:", hre.ethers.formatUnits(vestedAmount, 18), "BLOCKS");
  console.log("- Schedule cliff:", schedule.cliff.toString(), "seconds");
  console.log("- Schedule duration:", schedule.duration.toString(), "seconds");
  console.log("- Schedule start:", schedule.start.toString());
  
  // Get user stats from PackageManager
  console.log("\n📈 User Portfolio Stats:");
  const userStats = await packageManager.getUserStats(testUser);
  console.log("- Total Invested:", hre.ethers.formatUnits(userStats.totalInvested, 6), "USDT");
  console.log("- Total Tokens Received:", hre.ethers.formatUnits(userStats.totalTokensReceived, 18), "BLOCKS");
  console.log("- Total Vest Tokens:", hre.ethers.formatUnits(userStats.totalVestTokens, 18), "BLOCKS");
  console.log("- Total Pool Tokens:", hre.ethers.formatUnits(userStats.totalPoolTokens, 18), "BLOCKS");
  
  // Calculate expected values based on investment
  console.log("\n🧮 Expected Values Analysis:");
  const totalInvestedUSDT = Number(hre.ethers.formatUnits(userStats.totalInvested, 6));
  console.log("Total invested:", totalInvestedUSDT, "USDT");
  
  // Assuming 2.0 USDT per BLOCKS exchange rate and 70% vesting
  const expectedTotalTokens = totalInvestedUSDT / 2.0; // 2.0 USDT per BLOCKS
  const expectedVestTokens = expectedTotalTokens * 0.7; // 70% vesting
  const expectedPoolTokens = expectedTotalTokens * 0.3; // 30% pool
  
  console.log("Expected total tokens:", expectedTotalTokens, "BLOCKS");
  console.log("Expected vest tokens:", expectedVestTokens, "BLOCKS");
  console.log("Expected pool tokens:", expectedPoolTokens, "BLOCKS");
  
  // Calculate the inflation factor
  const actualVestTokens = Number(hre.ethers.formatUnits(userStats.totalVestTokens, 18));
  const inflationFactor = actualVestTokens / expectedVestTokens;
  console.log("\n🚨 Inflation Analysis:");
  console.log("Actual vest tokens:", actualVestTokens.toLocaleString(), "BLOCKS");
  console.log("Expected vest tokens:", expectedVestTokens, "BLOCKS");
  console.log("Inflation factor:", inflationFactor.toLocaleString());
  
  // Check if totalLocked matches totalVestTokens
  const totalLockedFormatted = Number(hre.ethers.formatUnits(totalLocked, 18));
  console.log("\n🔗 Vesting Vault vs Portfolio Stats:");
  console.log("VestingVault.totalLocked:", totalLockedFormatted.toLocaleString(), "BLOCKS");
  console.log("PackageManager.totalVestTokens:", actualVestTokens.toLocaleString(), "BLOCKS");
  console.log("Values match:", Math.abs(totalLockedFormatted - actualVestTokens) < 0.001);
  
  // Calculate what the corrected value should be
  const correctionFactor = expectedVestTokens / actualVestTokens;
  console.log("\n✅ Correction Factor Needed:");
  console.log("Correction factor:", correctionFactor);
  console.log("Corrected value:", (actualVestTokens * correctionFactor).toFixed(4), "BLOCKS");
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
