<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <!-- Favicon -->
    <link rel="icon" type="image/png" sizes="32x32" href="/Blockcooplogo.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/Blockcooplogo.png" />
    <link rel="shortcut icon" href="/favicon.ico" />

    <!-- Apple Touch Icon -->
    <link rel="apple-touch-icon" sizes="180x180" href="/Blockcooplogo.png" />

    <!-- Android Chrome -->
    <link rel="icon" type="image/png" sizes="192x192" href="/Blockcooplogo.png" />
    <link rel="icon" type="image/png" sizes="512x512" href="/Blockcooplogo.png" />

    <!-- Web App Manifest -->
    <link rel="manifest" href="/manifest.json" />

    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>BlockCoop Sacco - DeFi Investment Platform</title>
    <meta name="description" content="Sophisticated investment packages with advanced splitting mechanisms, vesting schedules, and secondary market integration on BSC." />

    <!-- Theme Color for Mobile Browsers -->
    <meta name="theme-color" content="#2563eb" />
    <meta name="msapplication-TileColor" content="#2563eb" />
    <meta name="msapplication-TileImage" content="/Blockcooplogo.png" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:title" content="BlockCoop Sacco - DeFi Investment Platform" />
    <meta property="og:description" content="Invest in packages, earn annual dividends with blockchain transparency and SACCO principles." />
    <meta property="og:image" content="/Blockcooplogo.png" />

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="BlockCoop Sacco - DeFi Investment Platform" />
    <meta name="twitter:description" content="Invest in packages, earn annual dividends with blockchain transparency and SACCO principles." />
    <meta name="twitter:image" content="/Blockcooplogo.png" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>