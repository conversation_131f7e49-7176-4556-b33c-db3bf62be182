const hre = require("hardhat");

async function main() {
  console.log("🔍 Debugging DEX Pool Liquidity Issue...");
  
  // Load contract addresses from deployments
  const deployments = require('./deployments/deployments-corrected-portfolio-metrics.json');
  
  // Get contracts
  const blocksToken = await hre.ethers.getContractAt("BLOCKS", deployments.contracts.BLOCKS);
  const usdtToken = await hre.ethers.getContractAt("IERC20", deployments.contracts.USDT);
  const factory = await hre.ethers.getContractAt("IPancakeFactory", deployments.contracts.PancakeFactory);
  
  console.log("\n📊 DEX Pool Analysis:");
  console.log("BLOCKS Token:", deployments.contracts.BLOCKS);
  console.log("USDT Token:", deployments.contracts.USDT);
  console.log("Factory:", deployments.contracts.PancakeFactory);
  
  // Check if pair exists
  let pairAddress;
  try {
    pairAddress = await factory.getPair(deployments.contracts.BLOCKS, deployments.contracts.USDT);
    console.log("Pair address:", pairAddress);
  } catch (error) {
    console.log("❌ Error getting pair address:", error.message);
    console.log("This likely means the factory interface is different or the pair doesn't exist");

    // Try alternative approach - check if pair exists by trying to create one
    console.log("Assuming no pair exists...");
    pairAddress = hre.ethers.ZeroAddress;
  }
  
  if (pairAddress === hre.ethers.ZeroAddress) {
    console.log("❌ No liquidity pair exists for BLOCKS/USDT");
    console.log("This explains why Total Pool Liquidity shows a low value - there's no actual liquidity pool yet!");
    return;
  }
  
  // Get pair contract
  const pair = await hre.ethers.getContractAt("IPancakePair", pairAddress);
  
  // Get reserves
  const [reserve0, reserve1] = await pair.getReserves();
  const token0 = await pair.token0();
  const token1 = await pair.token1();
  const totalSupply = await pair.totalSupply();
  
  console.log("\n💰 Pool Reserves:");
  console.log("Token0:", token0);
  console.log("Token1:", token1);
  console.log("Reserve0:", hre.ethers.formatUnits(reserve0, 18), "tokens");
  console.log("Reserve1:", hre.ethers.formatUnits(reserve1, 18), "tokens");
  console.log("Total LP Supply:", hre.ethers.formatUnits(totalSupply, 18), "LP tokens");
  
  // Determine which token is which
  const isBlocksToken0 = token0.toLowerCase() === deployments.contracts.BLOCKS.toLowerCase();
  const blocksReserve = isBlocksToken0 ? reserve0 : reserve1;
  const usdtReserve = isBlocksToken0 ? reserve1 : reserve0;
  
  console.log("\n🔍 Identified Reserves:");
  console.log("BLOCKS Reserve:", hre.ethers.formatUnits(blocksReserve, 18), "BLOCKS");
  console.log("USDT Reserve:", hre.ethers.formatUnits(usdtReserve, 6), "USDT");
  
  // Calculate prices
  const blocksReserveFormatted = Number(hre.ethers.formatUnits(blocksReserve, 18));
  const usdtReserveFormatted = Number(hre.ethers.formatUnits(usdtReserve, 6));
  
  const blocksPrice = usdtReserveFormatted / blocksReserveFormatted;
  const usdtPrice = blocksReserveFormatted / usdtReserveFormatted;
  
  console.log("\n💲 Token Prices:");
  console.log("BLOCKS Price:", blocksPrice.toFixed(6), "USDT per BLOCKS");
  console.log("USDT Price:", usdtPrice.toFixed(6), "BLOCKS per USDT");
  
  // Calculate total liquidity using the formula from dexUtils.ts
  // totalLiquidity = reserveUSDT * 2n (line 181 in dexUtils.ts)
  const totalLiquidityRaw = usdtReserve * 2n;
  const totalLiquidityFormatted = Number(hre.ethers.formatUnits(totalLiquidityRaw, 6));
  
  console.log("\n🏊 Pool Liquidity Calculation:");
  console.log("Formula: totalLiquidity = reserveUSDT * 2");
  console.log("USDT Reserve:", usdtReserveFormatted, "USDT");
  console.log("Total Liquidity (raw):", totalLiquidityRaw.toString(), "wei");
  console.log("Total Liquidity (formatted):", totalLiquidityFormatted.toFixed(2), "USDT");
  console.log("Total Liquidity (display):", `$${totalLiquidityFormatted.toFixed(2)}`);
  
  // Alternative calculation: USDT value + BLOCKS value in USDT terms
  const blocksValueInUSDT = blocksReserveFormatted * blocksPrice;
  const totalLiquidityAlternative = usdtReserveFormatted + blocksValueInUSDT;
  
  console.log("\n🔄 Alternative Calculation:");
  console.log("USDT Reserve Value:", usdtReserveFormatted.toFixed(2), "USDT");
  console.log("BLOCKS Reserve Value:", blocksValueInUSDT.toFixed(2), "USDT");
  console.log("Total Liquidity (alternative):", totalLiquidityAlternative.toFixed(2), "USDT");
  
  // Check if the low value is due to small reserves
  console.log("\n🚨 Analysis:");
  if (totalLiquidityFormatted < 100) {
    console.log("⚠️  Pool liquidity is indeed low (<$100)");
    console.log("This could be because:");
    console.log("1. The pool was recently created with minimal liquidity");
    console.log("2. Most liquidity has been removed");
    console.log("3. The pool is not being used actively");
  } else {
    console.log("✅ Pool liquidity appears normal");
  }
  
  // Check if there are any correction factors that should be applied
  console.log("\n🔧 Checking for Inflation Issues:");
  if (blocksReserveFormatted > 1000000) { // More than 1M BLOCKS
    console.log("⚠️  BLOCKS reserve appears inflated:", blocksReserveFormatted.toLocaleString());
    console.log("This could affect the liquidity calculation");
    
    // Calculate what the corrected liquidity would be
    const correctionFactor = 1 / 1347985347985.348; // From vesting analysis
    const correctedBlocksReserve = blocksReserveFormatted * correctionFactor;
    const correctedBlocksValueInUSDT = correctedBlocksReserve * blocksPrice;
    const correctedTotalLiquidity = usdtReserveFormatted + correctedBlocksValueInUSDT;
    
    console.log("Corrected BLOCKS reserve:", correctedBlocksReserve.toFixed(4), "BLOCKS");
    console.log("Corrected total liquidity:", correctedTotalLiquidity.toFixed(2), "USDT");
  } else {
    console.log("✅ BLOCKS reserve appears normal");
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
