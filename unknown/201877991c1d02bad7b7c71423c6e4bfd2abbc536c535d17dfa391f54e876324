name: M-Pesa Integration Tests

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'backend/**'
      - 'src/**'
      - 'package*.json'
      - '.github/workflows/mpesa-tests.yml'
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'backend/**'
      - 'src/**'
      - 'package*.json'
      - '.github/workflows/mpesa-tests.yml'

env:
  NODE_VERSION: '18'

jobs:
  # Backend testing job
  backend-tests:
    name: Backend Tests
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        test-type: [unit, integration, e2e, performance, security]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: backend/package-lock.json

      - name: Install backend dependencies
        run: |
          cd backend
          npm ci

      - name: Setup test environment
        run: |
          cd backend
          cp .env.example .env.test
          mkdir -p tests/logs

      - name: Run ${{ matrix.test-type }} tests
        run: |
          cd backend
          npm run test:${{ matrix.test-type }}
        env:
          NODE_ENV: test
          DATABASE_URL: ':memory:'

      - name: Upload test results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: backend-test-results-${{ matrix.test-type }}
          path: |
            backend/tests/logs/
            backend/coverage/
          retention-days: 7

  # Backend coverage job
  backend-coverage:
    name: Backend Coverage
    runs-on: ubuntu-latest
    needs: backend-tests
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: backend/package-lock.json

      - name: Install backend dependencies
        run: |
          cd backend
          npm ci

      - name: Setup test environment
        run: |
          cd backend
          cp .env.example .env.test

      - name: Run tests with coverage
        run: |
          cd backend
          npm run test:coverage
        env:
          NODE_ENV: test
          DATABASE_URL: ':memory:'

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: backend/coverage/lcov.info
          flags: backend
          name: backend-coverage

      - name: Comment coverage on PR
        if: github.event_name == 'pull_request'
        uses: romeovs/lcov-reporter-action@v0.3.1
        with:
          lcov-file: backend/coverage/lcov.info
          github-token: ${{ secrets.GITHUB_TOKEN }}
          title: Backend Coverage Report

  # Frontend testing job
  frontend-tests:
    name: Frontend Tests
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run frontend tests
        run: npm run test:run

      - name: Run frontend tests with coverage
        run: npm run test:coverage

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: coverage/lcov.info
          flags: frontend
          name: frontend-coverage

      - name: Upload test results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: frontend-test-results
          path: |
            coverage/
            test-results/
          retention-days: 7

  # Lint and code quality
  code-quality:
    name: Code Quality
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run ESLint
        run: npm run lint

      - name: Run TypeScript check
        run: npx tsc --noEmit

      - name: Check code formatting
        run: npx prettier --check "src/**/*.{ts,tsx,js,jsx}"

  # Security scanning
  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run npm audit
        run: npm audit --audit-level=moderate

      - name: Install backend dependencies
        run: |
          cd backend
          npm ci

      - name: Run backend npm audit
        run: |
          cd backend
          npm audit --audit-level=moderate

      - name: Run Snyk security scan
        if: github.event_name == 'push' && github.ref == 'refs/heads/main'
        uses: snyk/actions/node@master
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          args: --severity-threshold=high

  # Integration test with real M-Pesa sandbox (only on main branch)
  sandbox-integration:
    name: M-Pesa Sandbox Integration
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    needs: [backend-tests, frontend-tests]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: backend/package-lock.json

      - name: Install backend dependencies
        run: |
          cd backend
          npm ci

      - name: Setup M-Pesa sandbox environment
        run: |
          cd backend
          echo "NODE_ENV=test" > .env.test
          echo "MPESA_CONSUMER_KEY=${{ secrets.MPESA_SANDBOX_CONSUMER_KEY }}" >> .env.test
          echo "MPESA_CONSUMER_SECRET=${{ secrets.MPESA_SANDBOX_CONSUMER_SECRET }}" >> .env.test
          echo "MPESA_BUSINESS_SHORT_CODE=${{ secrets.MPESA_SANDBOX_BUSINESS_SHORT_CODE }}" >> .env.test
          echo "MPESA_PASSKEY=${{ secrets.MPESA_SANDBOX_PASSKEY }}" >> .env.test
          echo "MPESA_ENVIRONMENT=sandbox" >> .env.test

      - name: Run sandbox tests
        run: |
          cd backend
          npm run test:sandbox
        continue-on-error: true

      - name: Upload sandbox test results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: sandbox-test-results
          path: backend/tests/logs/
          retention-days: 7

  # Performance benchmarking
  performance-benchmark:
    name: Performance Benchmark
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'
    needs: backend-tests
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: backend/package-lock.json

      - name: Install backend dependencies
        run: |
          cd backend
          npm ci

      - name: Run performance tests
        run: |
          cd backend
          npm run test:performance
        env:
          NODE_ENV: test
          DATABASE_URL: ':memory:'

      - name: Comment performance results on PR
        if: always()
        uses: actions/github-script@v7
        with:
          script: |
            const fs = require('fs');
            const path = 'backend/tests/logs/test-report.json';
            
            if (fs.existsSync(path)) {
              const report = JSON.parse(fs.readFileSync(path, 'utf8'));
              const performanceSuite = report.suites.find(s => s.name.includes('Performance'));
              
              if (performanceSuite) {
                const comment = `## Performance Test Results
                
                **Duration**: ${performanceSuite.duration}ms
                **Status**: ${performanceSuite.status}
                
                Performance tests help ensure the M-Pesa integration maintains acceptable response times.
                `;
                
                github.rest.issues.createComment({
                  issue_number: context.issue.number,
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  body: comment
                });
              }
            }

  # Test summary and notification
  test-summary:
    name: Test Summary
    runs-on: ubuntu-latest
    needs: [backend-tests, frontend-tests, code-quality]
    if: always()
    
    steps:
      - name: Download all test artifacts
        uses: actions/download-artifact@v4

      - name: Create test summary
        uses: actions/github-script@v7
        with:
          script: |
            const fs = require('fs');
            
            let summary = '# M-Pesa Integration Test Summary\n\n';
            
            // Check job results
            const jobs = [
              { name: 'Backend Tests', result: '${{ needs.backend-tests.result }}' },
              { name: 'Frontend Tests', result: '${{ needs.frontend-tests.result }}' },
              { name: 'Code Quality', result: '${{ needs.code-quality.result }}' }
            ];
            
            jobs.forEach(job => {
              const icon = job.result === 'success' ? '✅' : job.result === 'failure' ? '❌' : '⚠️';
              summary += `${icon} **${job.name}**: ${job.result}\n`;
            });
            
            summary += '\n## Test Coverage\n';
            summary += 'Coverage reports are available in the job artifacts.\n';
            
            summary += '\n## Next Steps\n';
            if (jobs.some(job => job.result === 'failure')) {
              summary += '- Review failed tests and fix issues\n';
              summary += '- Check test logs in job artifacts\n';
            } else {
              summary += '- All tests passed! ✨\n';
              summary += '- Ready for deployment\n';
            }
            
            core.summary.addRaw(summary);
            await core.summary.write();
