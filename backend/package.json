{"name": "blockcoop-mpesa-backend", "version": "1.0.0", "description": "Backend API for M-Pesa payment integration with BlockCoop Sacco", "main": "src/server.js", "type": "module", "scripts": {"start": "node src/server.js", "start:simple": "node src/simpleServer.js", "dev": "nodemon src/server.js", "test": "node scripts/runTests.js", "test:unit": "node scripts/runTests.js unit", "test:integration": "node scripts/runTests.js integration", "test:e2e": "node scripts/runTests.js e2e", "test:performance": "node scripts/runTests.js performance", "test:security": "node scripts/runTests.js security", "test:sandbox": "node scripts/runTests.js sandbox", "test:coverage": "node scripts/runTests.js coverage", "test:ci": "node scripts/runTests.js ci", "test:watch": "jest --watch", "db:migrate": "node src/database/migrate.js", "db:seed": "node src/database/seed.js"}, "dependencies": {"axios": "^1.6.0", "bcryptjs": "^2.4.3", "better-sqlite3": "^12.2.0", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "ethers": "^6.8.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "sqlite3": "^5.1.6", "uuid": "^9.0.1", "winston": "^3.11.0"}, "devDependencies": {"@types/jest": "^29.5.8", "jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "keywords": ["mpesa", "blockchain", "usdt", "payment", "api", "nodejs", "express"], "author": "BlockCoop Sacco", "license": "MIT"}