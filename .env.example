# BlockCoop Sacco Environment Configuration
# Copy this file to .env and fill in your actual values

# Blockchain Configuration
VITE_CHAIN_ID=97
VITE_RPC_URL=https://data-seed-prebsc-1-s1.binance.org:8545/
BSC_TESTNET_RPC=https://bsc-testnet.public.blastapi.io

# Contract Addresses (BSC Testnet)
VITE_USDT_ADDRESS=your_usdt_contract_address
VITE_SHARE_ADDRESS=your_share_contract_address
VITE_LP_ADDRESS=your_lp_contract_address
VITE_VAULT_ADDRESS=your_vault_contract_address
VITE_TAX_ADDRESS=your_tax_contract_address
VITE_PACKAGE_MANAGER_ADDRESS=your_package_manager_address

# PancakeSwap V2 BSC Testnet Addresses
VITE_ROUTER_ADDRESS=******************************************
VITE_FACTORY_ADDRESS=******************************************

# API Keys and Secrets (NEVER commit real values)
ADMIN_PRIVATE_KEY=your_admin_private_key_here
PRIVATE_KEY=your_private_key_here
ETHERSCAN_API_KEY=your_etherscan_api_key_here
VITE_WALLETCONNECT_PROJECT_ID=your_walletconnect_project_id_here

# M-Pesa API Configuration
VITE_MPESA_API_URL=http://localhost:3001/api